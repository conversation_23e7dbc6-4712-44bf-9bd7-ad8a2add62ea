FROM oven/bun:1.2.15-alpine AS testing-base

# Set metadata
LABEL maintainer="user"
LABEL description="Testing base image with <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> and C<PERSON><PERSON>ber"
LABEL version="1.0"
LABEL org.opencontainers.image.source="https://github.com/san4osq/beauty-crm"
LABEL org.opencontainers.image.licenses="ISC"
LABEL com.beauty-crm.alpine.version="3.19"
LABEL com.beauty-crm.bun.version="1.2.15"
LABEL com.beauty-crm.biome.version="latest"
LABEL com.beauty-crm.playwright.version="latest"
LABEL com.beauty-crm.cucumber.version="latest"

# Install system dependencies for Playwright browsers
RUN apk add --no-cache \
    ca-certificates \
    chromium \
    firefox \
    webkit2gtk \
    fonts-noto \
    && rm -rf /var/cache/apk/* /tmp/* /var/tmp/*

# Create non-root user
RUN addgroup -g 1001 -S bunjs && \
    adduser -S bunjs -u 1001 -G bunjs

# Set working directory
WORKDIR /app

# Install global packages
RUN bun add -g @biomejs/biome playwright @cucumber/cucumber

# Install Playwright browsers
RUN bunx playwright install chromium firefox webkit

# Create version info script
RUN echo '#!/bin/sh' > /usr/local/bin/show-versions && \
    echo 'echo "=== Beauty CRM Testing Base Image Info ==="' >> /usr/local/bin/show-versions && \
    echo 'echo "Image Created: $(date)"' >> /usr/local/bin/show-versions && \
    echo 'echo ""' >> /usr/local/bin/show-versions && \
    echo 'echo "=== Version Information ==="' >> /usr/local/bin/show-versions && \
    echo 'echo "Bun Version: $(bun --version)"' >> /usr/local/bin/show-versions && \
    echo 'echo "Alpine Version: $(cat /etc/alpine-release)"' >> /usr/local/bin/show-versions && \
    echo 'echo "Biome Version: $(bunx biome --version || echo \"Not available\")"' >> /usr/local/bin/show-versions && \
    echo 'echo "Playwright Version: $(bunx playwright --version || echo \"Not available\")"' >> /usr/local/bin/show-versions && \
    echo 'echo "Cucumber Version: $(bunx cucumber --version || echo \"Not available\")"' >> /usr/local/bin/show-versions && \
    echo 'echo ""' >> /usr/local/bin/show-versions && \
    echo 'echo "=== System Information ==="' >> /usr/local/bin/show-versions && \
    echo 'echo "CPU Architecture: $(uname -m)"' >> /usr/local/bin/show-versions && \
    echo 'echo "Operating System: $(uname -s)"' >> /usr/local/bin/show-versions && \
    echo 'echo "Kernel Version: $(uname -r)"' >> /usr/local/bin/show-versions && \
    echo 'echo ""' >> /usr/local/bin/show-versions && \
    echo 'echo "=== Browser Information ==="' >> /usr/local/bin/show-versions && \
    echo 'echo "Chromium: $(chromium --version || echo \"Not available\")"' >> /usr/local/bin/show-versions && \
    echo 'echo "Firefox: $(firefox --version || echo \"Not available\")"' >> /usr/local/bin/show-versions && \
    chmod +x /usr/local/bin/show-versions

# Change ownership of app directory
RUN chown -R bunjs:bunjs /app

# Run version check and store in metadata
RUN /usr/local/bin/show-versions > /app/testing-build-info.txt

# Switch to non-root user
USER bunjs

# Set environment
ENV NODE_ENV="test"
ENV PLAYWRIGHT_BROWSERS_PATH=/app/.playwright

# Default command
CMD ["sh", "-c", "cat /app/testing-build-info.txt && sh"] 