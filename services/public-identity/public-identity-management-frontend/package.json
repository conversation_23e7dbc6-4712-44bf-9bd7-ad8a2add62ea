{"author": "<PERSON><PERSON><PERSON><PERSON> <user>", "dependencies": {"@beauty-crm/platform-introvertic-ui": "workspace:*", "@tanstack/react-query": "^5.67.2", "axios": "^1.8.2", "date-fns": "^4.1.0", "jwt-decode": "^4.0.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-error-boundary": "^5.0.0", "react-icons": "^5.5.0", "react-router-dom": "^7.3.0", "web-vitals": "^4.2.4", "zod": "^3.24.2"}, "description": "Public Identity Management solution frontend", "devDependencies": {"@playwright/test": "^1.53.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "jsdom": "^26.0.0", "typescript": "^5.8.3", "vite": "^6.2.1", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.1.4"}, "directories": {"lib": "lib", "test": "test"}, "keywords": [], "license": "ISC", "main": "index.js", "module": "index.ts", "name": "@beauty-crm/public-identity-management-frontend", "private": true, "resolutions": {"@biomejs/biome": "npm:@biomejs/wasm-web"}, "scripts": {"build": "tsc && nx vite:build", "dev": "vite", "format": "biome format --write --fix .", "lint": "biome lint .", "lint:check": "biome check .", "lint:fix": "biome lint --write .", "preview": "nx vite:preview", "test": "nx vite:test run", "test:watch": "nx vite:test", "ui:check": "node ./scripts/ui-check.mjs", "ui:simple": "node ./scripts/simple-ui-check.js"}, "type": "module", "version": "1.0.0"}