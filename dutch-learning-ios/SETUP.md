# Dutch Learning iOS App - Setup Guide

## 🚀 Quick Start Options

### Option 1: React Native + Expo (Native iOS App)

**Prerequisites:**
- Node.js 18+ installed
- npm or yarn package manager
- Expo CLI

**Step 1: Install Expo CLI**
```bash
npm install -g @expo/cli
```

**Step 2: Setup Project**
```bash
cd dutch-learning-ios/react-native-app
npm install
```

**Step 3: Start Development**
```bash
expo start
```

**Step 4: Test on iPhone**
1. Install "Expo Go" app from App Store
2. Scan QR code from terminal with iPhone camera
3. App will open in Expo Go for testing

**Step 5: Build Native iOS App (Cloud Build)**
```bash
# Install EAS CLI
npm install -g eas-cli

# Login to Expo account (create free account at expo.dev)
eas login

cd dutch

# Configure build
eas build:configure

# Build iOS app
eas build --platform ios
```

**Step 6: Install on iPhone XS**
1. Download IPA file from build completion email
2. Use AltStore, Sideloadly, or Xcode to install IPA
3. Trust developer certificate in Settings → General → VPN & Device Management

### Option 2: Progressive Web App (Instant Setup)

**Step 1: Serve PWA Locally**
```bash
cd dutch-learning-ios/pwa-app

# Using Python (built-in on macOS)
python3 -m http.server 8000

# Or using Node.js
npx serve .

# Or using PHP
php -S localhost:8000
```

**Step 2: Access on iPhone**
1. Open Safari on iPhone
2. Navigate to `http://[your-computer-ip]:8000`
3. Tap Share button → "Add to Home Screen"
4. App appears on home screen like native app

**Step 3: Deploy to Web (Optional)**
Upload PWA files to any web hosting service:
- GitHub Pages
- Netlify
- Vercel
- Firebase Hosting

## 🔧 Development Workflow

### React Native Development
```bash
# Start development server
expo start

# Start with specific platform
expo start --ios
expo start --android

# Clear cache if needed
expo start --clear

# View logs
expo logs
```

### PWA Development
```bash
# Serve locally for testing
cd pwa-app
python3 -m http.server 8000

# Test PWA features
# - Add to home screen
# - Offline functionality
# - Native-like behavior
```

## 📱 Testing on iPhone XS

### React Native App Testing
1. **Expo Go Method** (Easiest):
   - Install Expo Go from App Store
   - Scan QR code from `expo start`
   - Test immediately on device

2. **TestFlight Method** (Production-like):
   - Build with `eas build --platform ios`
   - Upload to TestFlight via `eas submit`
   - Install via TestFlight app

3. **Direct Install Method** (Advanced):
   - Build IPA with EAS
   - Use AltStore or similar to sideload
   - Requires developer certificate trust

### PWA Testing
1. **Safari Method**:
   - Open in Safari browser
   - Use "Add to Home Screen"
   - Test offline functionality

2. **Web Inspector**:
   - Connect iPhone to Mac
   - Use Safari Web Inspector for debugging
   - Monitor console logs and performance

## 🛠 Troubleshooting

### React Native Issues

**Metro bundler not starting:**
```bash
expo start --clear
# or
npx expo start --clear
```

**iOS build failing:**
```bash
# Check EAS build logs
eas build:list

# Update dependencies
npm update
```

**Expo Go not connecting:**
- Ensure iPhone and computer on same WiFi
- Try tunnel mode: `expo start --tunnel`
- Check firewall settings

### PWA Issues

**App not installing:**
- Ensure HTTPS (or localhost)
- Check manifest.json validity
- Verify service worker registration

**Offline not working:**
- Check service worker cache
- Verify network requests in dev tools
- Update cache version in sw.js

**Audio not playing:**
- Test in Safari (not Chrome on iOS)
- Check iOS audio permissions
- Verify speechSynthesis API support

## 📊 Performance Optimization

### React Native
- Use Flipper for debugging
- Profile with React DevTools
- Optimize images and assets
- Minimize bundle size

### PWA
- Optimize service worker caching
- Compress images and assets
- Use lazy loading for content
- Minimize JavaScript bundle

## 🔐 iOS Deployment

### App Store Deployment (React Native)
1. Build production app: `eas build --platform ios`
2. Submit to App Store: `eas submit --platform ios`
3. Configure App Store Connect
4. Submit for review

### Enterprise/Internal Distribution
1. Use Apple Developer Enterprise Program
2. Build with enterprise certificate
3. Distribute via internal channels
4. No App Store review required

### PWA Distribution
1. Host on HTTPS domain
2. Share URL with users
3. Users add to home screen
4. No app store required

## 📚 Adding More Content

### Expanding Sentence Database
1. Edit `data/sentences.json` (React Native)
2. Add to `sentences` array in `index.html` (PWA)
3. Include Dutch sentence, English translation, answer, difficulty
4. Test new content thoroughly

### Adding Features
- Spaced repetition algorithm
- User accounts and sync
- Achievement system
- More language pairs
- Advanced statistics

## 🎯 Next Steps

1. **Test both versions** on iPhone XS
2. **Choose preferred approach** (React Native vs PWA)
3. **Customize content** with more Dutch sentences
4. **Add advanced features** based on usage
5. **Deploy to production** when ready

## 📞 Support

For issues or questions:
1. Check troubleshooting section above
2. Review Expo documentation (React Native)
3. Test PWA in Safari Web Inspector
4. Verify iOS 18.2 compatibility
