import React, { useState, useEffect, useRef } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TextInput,
  TouchableOpacity,
  Alert,
  SafeAreaView,
  ScrollView,
  Dimensions,
  Animated,
  Easing,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import * as Speech from 'expo-speech';
import AsyncStorage from '@react-native-async-storage/async-storage';
import sentencesData from './data/sentences.json';

const { width, height } = Dimensions.get('window');

export default function App() {
  const [currentSentence, setCurrentSentence] = useState(null);
  const [userAnswer, setUserAnswer] = useState('');
  const [score, setScore] = useState(0);
  const [totalAnswered, setTotalAnswered] = useState(0);
  const [difficulty, setDifficulty] = useState('common');
  const [showAnswer, setShowAnswer] = useState(false);
  const [isCorrect, setIsCorrect] = useState(null);
  const [streak, setStreak] = useState(0);
  const [gameMode, setGameMode] = useState('text'); // text, multiple-choice, listening
  const [showTranslation, setShowTranslation] = useState(true);
  const [multipleChoiceOptions, setMultipleChoiceOptions] = useState([]);
  const [selectedChoice, setSelectedChoice] = useState(null);

  // Animation values
  const fadeAnim = useRef(new Animated.Value(1)).current;
  const scaleAnim = useRef(new Animated.Value(1)).current;
  const celebrationAnim = useRef(new Animated.Value(0)).current;

  // Load progress from storage
  useEffect(() => {
    loadProgress();
    loadNextSentence();
  }, [difficulty]);

  const loadProgress = async () => {
    try {
      const savedScore = await AsyncStorage.getItem('score');
      const savedTotal = await AsyncStorage.getItem('totalAnswered');
      const savedStreak = await AsyncStorage.getItem('streak');

      if (savedScore) setScore(parseInt(savedScore));
      if (savedTotal) setTotalAnswered(parseInt(savedTotal));
      if (savedStreak) setStreak(parseInt(savedStreak));
    } catch (error) {
      console.log('Error loading progress:', error);
    }
  };

  const saveProgress = async (newScore, newTotal, newStreak) => {
    try {
      await AsyncStorage.setItem('score', newScore.toString());
      await AsyncStorage.setItem('totalAnswered', newTotal.toString());
      await AsyncStorage.setItem('streak', newStreak.toString());
    } catch (error) {
      console.log('Error saving progress:', error);
    }
  };

  const generateMultipleChoiceOptions = (correctAnswer, allSentences) => {
    const wrongAnswers = allSentences
      .filter(s => s.answer.toLowerCase() !== correctAnswer.toLowerCase())
      .map(s => s.answer)
      .sort(() => Math.random() - 0.5)
      .slice(0, 3);

    const options = [correctAnswer, ...wrongAnswers].sort(() => Math.random() - 0.5);
    return options;
  };

  const loadNextSentence = () => {
    // Animate card transition
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 0.7,
        duration: 200,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 0.95,
        duration: 200,
        useNativeDriver: true,
      }),
    ]).start(() => {
      const filteredSentences = sentencesData.filter(s => s.difficulty === difficulty);
      const randomIndex = Math.floor(Math.random() * filteredSentences.length);
      const sentence = filteredSentences[randomIndex];

      setCurrentSentence(sentence);
      setUserAnswer('');
      setShowAnswer(false);
      setIsCorrect(null);
      setSelectedChoice(null);

      // Generate multiple choice options if needed
      if (gameMode === 'multiple-choice') {
        const options = generateMultipleChoiceOptions(sentence.answer, filteredSentences);
        setMultipleChoiceOptions(options);
      }

      // Animate back to normal
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 300,
          easing: Easing.elastic(1.2),
          useNativeDriver: true,
        }),
      ]).start();
    });
  };

  const checkAnswer = () => {
    if (!currentSentence || !userAnswer.trim()) {
      Alert.alert('Please enter an answer');
      return;
    }

    const correct = userAnswer.toLowerCase().trim() === currentSentence.answer.toLowerCase();
    setIsCorrect(correct);
    setShowAnswer(true);

    const newTotal = totalAnswered + 1;
    let newScore = score;
    let newStreak = streak;

    if (correct) {
      newScore += 1;
      newStreak += 1;
      // Trigger celebration animation
      celebrationAnim.setValue(0);
      Animated.sequence([
        Animated.timing(celebrationAnim, {
          toValue: 1,
          duration: 600,
          easing: Easing.elastic(1.2),
          useNativeDriver: true,
        }),
        Animated.timing(celebrationAnim, {
          toValue: 0,
          duration: 400,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      newStreak = 0;
    }

    setScore(newScore);
    setTotalAnswered(newTotal);
    setStreak(newStreak);
    saveProgress(newScore, newTotal, newStreak);
  };

  const speakSentence = () => {
    if (currentSentence) {
      Speech.speak(currentSentence.audio, {
        language: 'nl-NL',
        pitch: 1.0,
        rate: 0.8,
      });
    }
  };

  const nextSentence = () => {
    loadNextSentence();
  };

  const resetProgress = () => {
    Alert.alert(
      'Reset Progress',
      'Are you sure you want to reset all progress?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Reset',
          style: 'destructive',
          onPress: () => {
            setScore(0);
            setTotalAnswered(0);
            setStreak(0);
            saveProgress(0, 0, 0);
          },
        },
      ]
    );
  };

  const accuracy = totalAnswered > 0 ? Math.round((score / totalAnswered) * 100) : 0;

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="auto" />

      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>Dutch Learning</Text>
        <View style={styles.statsContainer}>
          <Text style={styles.stat}>Score: {score}/{totalAnswered}</Text>
          <Text style={styles.stat}>Accuracy: {accuracy}%</Text>
          <Text style={styles.stat}>Streak: {streak}</Text>
        </View>
      </View>

      {/* Difficulty Selector */}
      <View style={styles.difficultyContainer}>
        {['common', 'intermediate', 'advanced'].map((level) => (
          <TouchableOpacity
            key={level}
            style={[
              styles.difficultyButton,
              difficulty === level && styles.difficultyButtonActive
            ]}
            onPress={() => setDifficulty(level)}
          >
            <Text style={[
              styles.difficultyText,
              difficulty === level && styles.difficultyTextActive
            ]}>
              {level.charAt(0).toUpperCase() + level.slice(1)}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      <ScrollView style={styles.content}>
        {currentSentence && (
          <Animated.View
            style={[
              styles.exerciseContainer,
              {
                opacity: fadeAnim,
                transform: [{ scale: scaleAnim }],
              },
            ]}
          >
            {/* Dutch Sentence */}
            <View style={styles.sentenceContainer}>
              <Text style={styles.dutchSentence}>{currentSentence.dutch}</Text>
              <TouchableOpacity style={styles.speakerButton} onPress={speakSentence}>
                <Text style={styles.speakerIcon}>🔊</Text>
              </TouchableOpacity>
            </View>

            {/* English Translation */}
            <Text style={styles.englishSentence}>{currentSentence.english}</Text>

            {/* Answer Input */}
            <TextInput
              style={[
                styles.answerInput,
                isCorrect === true && styles.correctInput,
                isCorrect === false && styles.incorrectInput,
              ]}
              value={userAnswer}
              onChangeText={setUserAnswer}
              placeholder="Type your answer here..."
              placeholderTextColor="#999"
              autoCapitalize="none"
              autoCorrect={false}
              editable={!showAnswer}
              onSubmitEditing={() => {
                if (!showAnswer) {
                  checkAnswer();
                } else {
                  loadNextSentence();
                }
              }}
              returnKeyType={showAnswer ? "next" : "done"}
              blurOnSubmit={false}
            />

            {/* Answer Feedback */}
            {showAnswer && (
              <View style={styles.feedbackContainer}>
                <Text style={[
                  styles.feedbackText,
                  isCorrect ? styles.correctText : styles.incorrectText
                ]}>
                  {isCorrect ? '✅ Correct!' : '❌ Incorrect'}
                </Text>
                {!isCorrect && (
                  <Text style={styles.correctAnswer}>
                    Correct answer: {currentSentence.answer}
                  </Text>
                )}
              </View>
            )}

            {/* Action Buttons */}
            <View style={styles.buttonContainer}>
              {!showAnswer ? (
                <TouchableOpacity style={styles.checkButton} onPress={checkAnswer}>
                  <Text style={styles.buttonText}>Check Answer</Text>
                </TouchableOpacity>
              ) : (
                <TouchableOpacity style={styles.nextButton} onPress={nextSentence}>
                  <Text style={styles.buttonText}>Next Sentence</Text>
                </TouchableOpacity>
              )}
            </View>

            {/* Celebration Animation */}
            {isCorrect && (
              <Animated.Text
                style={[
                  styles.celebrationText,
                  {
                    opacity: celebrationAnim,
                    transform: [
                      {
                        scale: celebrationAnim.interpolate({
                          inputRange: [0, 1],
                          outputRange: [0, 1.5],
                        }),
                      },
                      {
                        rotate: celebrationAnim.interpolate({
                          inputRange: [0, 1],
                          outputRange: ['0deg', '360deg'],
                        }),
                      },
                    ],
                  },
                ]}
              >
                🎉
              </Animated.Text>
            )}
          </Animated.View>
        )}
      </ScrollView>

      {/* Footer */}
      <View style={styles.footer}>
        <TouchableOpacity style={styles.resetButton} onPress={resetProgress}>
          <Text style={styles.resetButtonText}>Reset Progress</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
  },
  header: {
    backgroundColor: 'rgba(33, 150, 243, 0.8)',
    padding: 20,
    paddingTop: 10,
    borderBottomLeftRadius: 25,
    borderBottomRightRadius: 25,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 15,
    elevation: 10,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: 'white',
    textAlign: 'center',
    marginBottom: 10,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  stat: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
  },
  difficultyContainer: {
    flexDirection: 'row',
    backgroundColor: 'white',
    margin: 10,
    borderRadius: 8,
    padding: 5,
  },
  difficultyButton: {
    flex: 1,
    padding: 10,
    alignItems: 'center',
    borderRadius: 6,
  },
  difficultyButtonActive: {
    backgroundColor: '#2196F3',
  },
  difficultyText: {
    fontSize: 14,
    color: '#666',
  },
  difficultyTextActive: {
    color: 'white',
    fontWeight: 'bold',
  },
  content: {
    flex: 1,
  },
  exerciseContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.25)',
    margin: 15,
    padding: 25,
    borderRadius: 25,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 15,
    elevation: 10,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.18)',
    position: 'relative',
  },
  celebrationText: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    fontSize: 60,
    zIndex: 1000,
    marginTop: -30,
    marginLeft: -30,
  },
  sentenceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  dutchSentence: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
    lineHeight: 28,
  },
  speakerButton: {
    padding: 10,
    marginLeft: 10,
  },
  speakerIcon: {
    fontSize: 24,
  },
  englishSentence: {
    fontSize: 16,
    color: '#666',
    marginBottom: 20,
    fontStyle: 'italic',
  },
  answerInput: {
    borderWidth: 2,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 15,
    fontSize: 18,
    backgroundColor: '#f9f9f9',
    marginBottom: 15,
  },
  correctInput: {
    borderColor: '#4CAF50',
    backgroundColor: '#f1f8e9',
  },
  incorrectInput: {
    borderColor: '#f44336',
    backgroundColor: '#ffebee',
  },
  feedbackContainer: {
    marginBottom: 15,
  },
  feedbackText: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 5,
  },
  correctText: {
    color: '#4CAF50',
  },
  incorrectText: {
    color: '#f44336',
  },
  correctAnswer: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    fontStyle: 'italic',
  },
  buttonContainer: {
    alignItems: 'center',
  },
  checkButton: {
    backgroundColor: '#2196F3',
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 8,
    minWidth: 150,
  },
  nextButton: {
    backgroundColor: '#4CAF50',
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 8,
    minWidth: 150,
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  footer: {
    padding: 10,
    alignItems: 'center',
  },
  resetButton: {
    padding: 10,
  },
  resetButtonText: {
    color: '#f44336',
    fontSize: 14,
  },
});
