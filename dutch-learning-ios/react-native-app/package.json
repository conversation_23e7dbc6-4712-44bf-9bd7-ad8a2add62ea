{"name": "dutch-learning-ios", "version": "1.0.0", "description": "Dutch language learning app for iOS - Clozemaster clone", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build:ios": "eas build --platform ios", "submit:ios": "eas submit --platform ios"}, "dependencies": {"expo": "~50.0.0", "expo-av": "~13.10.4", "expo-speech": "~11.7.0", "expo-status-bar": "~1.11.1", "react": "18.2.0", "react-native": "0.73.2", "react-native-async-storage": "^1.21.0", "react-native-vector-icons": "^10.0.3"}, "devDependencies": {"@babel/core": "^7.20.0"}, "keywords": ["dutch", "language-learning", "ios", "clo<PERSON><PERSON>", "education"], "author": "Dutch Learning App", "license": "MIT", "private": true}