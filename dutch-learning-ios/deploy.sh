#!/bin/bash

# Dutch Learning iOS App - Deployment Script
# This script helps deploy both React Native and PWA versions

set -e

echo "🇳🇱 Dutch Learning iOS App - Deployment Script"
echo "=============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "README.md" ] || [ ! -d "react-native-app" ] || [ ! -d "pwa-app" ]; then
    print_error "Please run this script from the dutch-learning-ios directory"
    exit 1
fi

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js 18+ from https://nodejs.org"
        exit 1
    fi
    
    NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        print_error "Node.js version 18+ required. Current version: $(node --version)"
        exit 1
    fi
    
    print_success "Node.js $(node --version) is installed"
    
    # Check npm
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed"
        exit 1
    fi
    
    print_success "npm $(npm --version) is installed"
}

# Function to setup React Native app
setup_react_native() {
    print_status "Setting up React Native app..."
    
    cd react-native-app
    
    # Install dependencies
    print_status "Installing dependencies..."
    npm install
    
    # Check if Expo CLI is installed
    if ! command -v expo &> /dev/null; then
        print_status "Installing Expo CLI..."
        npm install -g @expo/cli
    fi
    
    print_success "React Native app setup complete"
    cd ..
}

# Function to start React Native development
start_react_native() {
    print_status "Starting React Native development server..."
    cd react-native-app
    
    print_status "Starting Expo development server..."
    print_warning "Scan the QR code with your iPhone camera to open in Expo Go"
    print_warning "Make sure your iPhone and computer are on the same WiFi network"
    
    expo start
}

# Function to build React Native app
build_react_native() {
    print_status "Building React Native app for iOS..."
    cd react-native-app
    
    # Check if EAS CLI is installed
    if ! command -v eas &> /dev/null; then
        print_status "Installing EAS CLI..."
        npm install -g eas-cli
    fi
    
    print_warning "You need an Expo account to build. Create one at https://expo.dev"
    print_status "Logging into Expo..."
    eas login
    
    print_status "Configuring build..."
    eas build:configure
    
    print_status "Building iOS app (this may take 10-15 minutes)..."
    eas build --platform ios
    
    print_success "Build complete! Check your email for the download link."
    cd ..
}

# Function to serve PWA locally
serve_pwa() {
    print_status "Starting PWA development server..."
    cd pwa-app
    
    # Try different methods to serve the PWA
    if command -v python3 &> /dev/null; then
        print_status "Serving PWA with Python..."
        print_success "PWA is running at http://localhost:8000"
        print_warning "On your iPhone, open Safari and go to http://[your-computer-ip]:8000"
        print_warning "Then tap Share → Add to Home Screen"
        python3 -m http.server 8000
    elif command -v python &> /dev/null; then
        print_status "Serving PWA with Python 2..."
        print_success "PWA is running at http://localhost:8000"
        python -m SimpleHTTPServer 8000
    elif command -v php &> /dev/null; then
        print_status "Serving PWA with PHP..."
        print_success "PWA is running at http://localhost:8000"
        php -S localhost:8000
    else
        print_status "Installing serve package..."
        npm install -g serve
        print_success "PWA is running at http://localhost:3000"
        serve -s . -l 3000
    fi
}

# Function to get local IP address
get_local_ip() {
    if command -v ipconfig &> /dev/null; then
        # macOS
        LOCAL_IP=$(ipconfig getifaddr en0 2>/dev/null || ipconfig getifaddr en1 2>/dev/null || echo "localhost")
    elif command -v hostname &> /dev/null; then
        # Linux
        LOCAL_IP=$(hostname -I | awk '{print $1}' 2>/dev/null || echo "localhost")
    else
        LOCAL_IP="localhost"
    fi
    echo "$LOCAL_IP"
}

# Function to show deployment options
show_menu() {
    echo ""
    echo "Choose deployment option:"
    echo "1) Setup React Native app (install dependencies)"
    echo "2) Start React Native development (Expo Go)"
    echo "3) Build React Native app for iOS (cloud build)"
    echo "4) Serve PWA locally for testing"
    echo "5) Check prerequisites"
    echo "6) Show local IP address"
    echo "7) Exit"
    echo ""
}

# Main menu loop
main() {
    while true; do
        show_menu
        read -p "Enter your choice (1-7): " choice
        
        case $choice in
            1)
                check_prerequisites
                setup_react_native
                ;;
            2)
                start_react_native
                ;;
            3)
                build_react_native
                ;;
            4)
                LOCAL_IP=$(get_local_ip)
                print_status "Your local IP address is: $LOCAL_IP"
                print_warning "Use http://$LOCAL_IP:8000 on your iPhone"
                serve_pwa
                ;;
            5)
                check_prerequisites
                ;;
            6)
                LOCAL_IP=$(get_local_ip)
                print_success "Your local IP address is: $LOCAL_IP"
                print_status "Use this IP to access the PWA from your iPhone"
                ;;
            7)
                print_success "Goodbye! Happy learning Dutch! 🇳🇱"
                exit 0
                ;;
            *)
                print_error "Invalid option. Please choose 1-7."
                ;;
        esac
        
        echo ""
        read -p "Press Enter to continue..."
    done
}

# Run main function
main
