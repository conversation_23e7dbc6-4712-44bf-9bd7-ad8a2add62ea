{"ignorePaths": ["node_modules/**", "dist/**", "build/**", ".git/**"], "language": "en", "version": "0.2", "words": ["<PERSON><PERSON><PERSON><PERSON>", "baserow", "biome", "biomejs", "bstart", "bunfig", "bunx", "cdktf", "checkperiod", "circuitbreaker", "clickhouse", "cmdk", "comlink", "configmap", "cuid", "cspell", "daygrid", "debezium", "<PERSON><PERSON><PERSON>", "embla", "fallbackduration", "flyctl", "Focusable", "fullcalendar", "glideapps", "gnueabihf", "gsub", "healthcheck", "hono", "iaac", "initialinterval", "introvertic", "jetstream", "jsonr", "kvname", "lightningcss", "mdblaster", "microfrontend", "microfrontends", "msvc", "newyork", "nuxt", "<PERSON><PERSON><PERSON><PERSON>", "otplib", "paralleldrive", "persistance", "pgoutput", "pkill", "qrcode", "recoveryduration", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "shadcn", "signoz", "skywalking", "sonner", "stripprefix", "supabase", "synchronised", "tabler", "timegrid", "timeslot", "traefik", "UMUX", "Uncategorized", "uuidv", "vaul", "vite", "Vitest", "zxcvbn"]}