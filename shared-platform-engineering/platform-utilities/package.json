{"author": "<PERSON><PERSON><PERSON><PERSON> <user>", "description": "Shared utilities for Beauty CRM", "devDependencies": {"@types/node": "^22.15.29", "rimraf": "^6.0.1", "typescript": "^5.8.3", "vitest": "^3.2.0"}, "keywords": [], "license": "ISC", "main": "dist/index.js", "name": "@beauty-crm/platform-utilities", "private": true, "scripts": {"_prebuild": "computing-lifecycle generate tsconfig", "build": "tsc --build", "clean": "rimraf dist .tsbuildinfo", "dev": "tsc --build --watch", "format": "biome format --write . --config-path ../../biome.json", "lint": "biome lint . --config-path ../../biome.json", "lint:check": "biome check . --config-path ../../biome.json", "lint:fix": "biome lint --write . --config-path ../../biome.json", "test": "vitest --run"}, "type": "module", "types": "dist/index.d.ts", "version": "1.0.0"}