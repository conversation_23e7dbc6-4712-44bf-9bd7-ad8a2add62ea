{"author": "<PERSON><PERSON><PERSON><PERSON> <user>", "dependencies": {"@beauty-crm/platform-environment-names": "workspace:*"}, "description": "Shared system settings and features for Beauty CRM", "devDependencies": {"@types/node": "^22.15.21", "rimraf": "^6.0.1", "typescript": "^5.8.3"}, "keywords": [], "license": "ISC", "main": "dist/index.js", "name": "@beauty-crm/platform-system-settings", "private": true, "scripts": {"_prebuild": "computing-lifecycle generate tsconfig", "build": "tsc", "clean": "<PERSON><PERSON><PERSON> dist", "dev": "tsc --watch", "format": "biome format --write --fix .", "lint": "biome lint .", "lint:check": "biome check .", "lint:fix": "biome lint --write .", "test": "echo 'No tests specified for platform-system-settings' && exit 0"}, "type": "module", "types": "dist/index.d.ts", "version": "1.0.0"}