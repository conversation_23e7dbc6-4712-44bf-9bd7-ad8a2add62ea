{"author": "<PERSON><PERSON><PERSON><PERSON> <user>", "description": "Environment name constants and utilities for Beauty CRM", "devDependencies": {"@types/node": "^20.11.30", "tsup": "^8.0.2", "typescript": "^5.8.3"}, "exports": {".": {"import": "./dist/index.js", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}, "files": ["dist/**"], "keywords": [], "license": "ISC", "main": "./dist/index.js", "module": "./dist/index.js", "name": "@beauty-crm/platform-environment-names", "private": true, "scripts": {"build": "tsc --build", "build:bundle": "bunx --bun tsup", "clean": "rm -rf dist .tsbuildinfo", "dev": "tsc --build --watch", "dev:bundle": "bunx --bun tsup --watch", "format": "biome format --write --fix .", "lint": "biome lint .", "lint:check": "biome check .", "lint:fix": "biome lint --write .", "prepack": "bun run build"}, "type": "module", "types": "./dist/index.d.ts", "version": "1.0.0"}