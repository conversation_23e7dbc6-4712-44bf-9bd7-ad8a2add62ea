{"author": "<PERSON><PERSON><PERSON><PERSON> <user>", "bin": {"computing-lifecycle": "./dist/bin/cli.js"}, "dependencies": {"@hono/node-server": "^1.14.4", "autoprefixer": "^10.4.21", "cdktf": "^0.21.0", "chalk": "^5.4.1", "commander": "^14.0.0", "constructs": "^10.4.2", "dotenv": "^16.5.0", "hono": "^4.7.11", "postcss": "^8.5.4", "postcss-import": "^16.1.0", "tailwindcss": "^4.1.8", "vite": "^6.3.5", "winston": "^3.17.0"}, "description": "Platform engineering tooling for managing application lifecycle, deployment, and infrastructure automation", "devDependencies": {"@types/node": "^22.15.29", "@vitest/coverage-v8": "^3.2.0", "rimraf": "^6.0.1", "typescript": "^5.8.3", "vitest": "^3.2.0"}, "keywords": [], "license": "ISC", "main": "dist/index.js", "name": "@beauty-crm/platform-computing-lifecycle", "private": true, "scripts": {"build": "tsc --build", "check": "biome check --write .", "check:circular": "madge --circular --extensions ts .", "check:dependencies": "madge --image dependencies.svg --extensions ts ./services ./shared-packages", "clean": "<PERSON><PERSON><PERSON> dist", "deploy:dev": "flyctl deploy", "deploy:prod": "flyctl deploy", "dev": "tsc --watch", "format": "biome format --write --fix .", "infra:deploy": "cdktf deploy", "infra:destroy": "cdktf destroy", "infra:get": "cdktf get", "infra:init": "cdktf init", "infra:plan": "cdktf plan", "lint": "biome lint .", "lint:check": "biome check .", "lint:fix": "biome lint --write .", "outdated": "bun outdated", "postbuild": "chmod +x dist/bin/cli.js && npm link", "test": "nx vite:test", "test:coverage": "nx vite:test run --coverage", "test:watch": "nx vite:test"}, "types": "dist/index.d.ts", "version": "1.0.0"}