FROM oven/bun:1.2.15-alpine AS builder

# Set metadata
LABEL maintainer="user"
LABEL description="Base image with Bun on Alpine Linux"
LABEL version="1.0"
LABEL org.opencontainers.image.source="https://github.com/san4osq/beauty-crm"
LABEL org.opencontainers.image.licenses="ISC"
LABEL com.beauty-crm.alpine.version="3.19"
LABEL com.beauty-crm.bun.version="1.2.15"

# Install only essential dependencies
RUN apk add --no-cache \
    ca-certificates \
    && rm -rf /var/cache/apk/* /tmp/* /var/tmp/*

# Create non-root user
RUN addgroup -g 1001 -S bunjs && \
    adduser -S bunjs -u 1001 -G bunjs

# Set working directory
WORKDIR /app

# Create version info script
RUN echo '#!/bin/sh' > /usr/local/bin/show-versions && \
    echo 'echo "=== Beauty CRM Base Image Info ==="' >> /usr/local/bin/show-versions && \
    echo 'echo "Image Created: $(date)"' >> /usr/local/bin/show-versions && \
    echo 'echo ""' >> /usr/local/bin/show-versions && \
    echo 'echo "=== Version Information ==="' >> /usr/local/bin/show-versions && \
    echo 'echo "Bun Version: $(bun --version)"' >> /usr/local/bin/show-versions && \
    echo 'echo "Alpine Version: $(cat /etc/alpine-release)"' >> /usr/local/bin/show-versions && \
    echo 'echo ""' >> /usr/local/bin/show-versions && \
    echo 'echo "=== System Information ==="' >> /usr/local/bin/show-versions && \
    echo 'echo "CPU Architecture: $(uname -m)"' >> /usr/local/bin/show-versions && \
    echo 'echo "Operating System: $(uname -s)"' >> /usr/local/bin/show-versions && \
    echo 'echo "Kernel Version: $(uname -r)"' >> /usr/local/bin/show-versions && \
    echo 'echo ""' >> /usr/local/bin/show-versions && \
    echo 'echo "=== Package Sizes ==="' >> /usr/local/bin/show-versions && \
    echo 'echo "Bun Binary Size: $(du -sh /usr/local/bin/bun | cut -f1)"' >> /usr/local/bin/show-versions && \
    echo 'echo "Root Directory Size: $(du -sh / | cut -f1)"' >> /usr/local/bin/show-versions && \
    echo 'echo "App Directory Size: $(du -sh /app | cut -f1)"' >> /usr/local/bin/show-versions && \
    echo 'echo ""' >> /usr/local/bin/show-versions && \
    echo 'echo "=== Environment ==="' >> /usr/local/bin/show-versions && \
    echo 'echo "Working Directory: $(pwd)"' >> /usr/local/bin/show-versions && \
    echo 'echo "PATH: $PATH"' >> /usr/local/bin/show-versions && \
    chmod +x /usr/local/bin/show-versions

# Change ownership of app directory
RUN chown -R bunjs:bunjs /app

# Run version check and store in metadata
RUN /usr/local/bin/show-versions > /app/build-info.txt

# Switch to non-root user
USER bunjs

# Set environment
ENV NODE_ENV="production"

# Default command
CMD ["sh", "-c", "cat /app/build-info.txt && sh"]
