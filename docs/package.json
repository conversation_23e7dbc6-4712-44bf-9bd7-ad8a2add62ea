{"author": "<PERSON><PERSON><PERSON><PERSON> <user>", "dependencies": {"@scalar/api-reference": "^1.25.116"}, "description": "", "devDependencies": {"@vue/theme": "^2.3.0", "vitepress": "^1.6.3"}, "keywords": [], "license": "ISC", "main": "index.js", "name": "@beauty-crm/docs", "scripts": {"docs:build": "vitepress build", "docs:dev": "vitepress dev --host 0.0.0.0", "docs:serve": "vitepress serve", "test": "echo \"Error: no test specified\" && exit 1"}, "type": "module", "version": "1.0.0"}