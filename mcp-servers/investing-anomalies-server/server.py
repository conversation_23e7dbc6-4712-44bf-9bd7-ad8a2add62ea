#!/usr/bin/env python3
"""
Investing Anomalies MCP Server - <PERSON> Quantitative Approach
Real statistical analysis using actual market data and mathematical models
Focuses on Amsterdam Stock Exchange (Euronext Amsterdam) and Frankfurt Stock Exchange (XETRA)
"""

import json
import asyncio
import logging
import numpy as np
import pandas as pd
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
import statistics
import math
from scipy import stats
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import IsolationForest
import yfinance as yf
import requests

# MCP imports
from mcp.server import Server
from mcp.types import Tool, TextContent

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("investing-anomalies-server")

@dataclass
class AnomalyResult:
    """Structure for anomaly detection results - <PERSON> approach"""
    company_name: str
    ticker_symbol: str
    exchange: str
    industry: str
    anomaly_type: str
    statistical_significance: float  # p-value
    z_score: float  # Standard deviations from mean
    sharpe_ratio: float  # Risk-adjusted return
    information_ratio: float  # Excess return vs benchmark
    current_price: float
    predicted_price: float
    confidence_interval: Tuple[float, float]  # 95% CI
    expected_return: float  # Percentage
    max_drawdown: float  # Risk metric
    volatility: float  # Annualized volatility
    description: str
    mathematical_model: str  # Model used for prediction
    supporting_data: Dict[str, Any]

class QuantitativeAnalysisEngine:
    """Jim Simons-inspired quantitative analysis engine"""
    
    def __init__(self):
        self.cache = {}
        self.models = {}
        
    def get_real_market_data(self, ticker: str, period: str = "2y") -> pd.DataFrame:
        """Fetch real market data using yfinance"""
        try:
            stock = yf.Ticker(ticker)
            data = stock.history(period=period)
            if data.empty:
                logger.warning(f"No data found for {ticker}")
                return pd.DataFrame()
            
            # Calculate technical indicators
            data['Returns'] = data['Close'].pct_change()
            data['SMA_20'] = data['Close'].rolling(window=20).mean()
            data['SMA_50'] = data['Close'].rolling(window=50).mean()
            data['Volatility'] = data['Returns'].rolling(window=20).std() * np.sqrt(252)
            data['RSI'] = self._calculate_rsi(data['Close'])
            data['Bollinger_Upper'] = data['SMA_20'] + (data['Close'].rolling(window=20).std() * 2)
            data['Bollinger_Lower'] = data['SMA_20'] - (data['Close'].rolling(window=20).std() * 2)
            
            return data
        except Exception as e:
            logger.error(f"Error fetching data for {ticker}: {str(e)}")
            return pd.DataFrame()
    
    def _calculate_rsi(self, prices: pd.Series, window: int = 14) -> pd.Series:
        """Calculate Relative Strength Index"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
    
    def detect_statistical_anomalies(self, data: pd.DataFrame, ticker: str) -> List[Dict[str, Any]]:
        """Detect anomalies using Jim Simons' regime-aware statistical methods"""
        if data.empty or len(data) < 50:
            return []

        anomalies = []

        # 1. Regime Detection - Key Innovation from Renaissance Technologies
        returns = data['Returns'].dropna()
        if len(returns) > 60:
            # Detect regime changes using rolling correlation with market
            # High momentum stocks (like NVIDIA) operate in different regimes
            rolling_vol = returns.rolling(window=20).std()
            rolling_mean = returns.rolling(window=20).mean()

            # Current regime characteristics
            current_vol = rolling_vol.iloc[-1]
            current_mean = rolling_mean.iloc[-1]
            historical_vol = rolling_vol.mean()
            historical_mean = rolling_mean.mean()

            # Momentum Regime Detection (Jim Simons' key insight)
            momentum_score = (current_mean / historical_mean) if historical_mean != 0 else 0
            vol_regime = current_vol / historical_vol if historical_vol != 0 else 1

            # Only flag as anomaly if BOTH momentum and volatility are extreme
            if momentum_score > 2.0 and vol_regime < 0.8:  # High momentum, low volatility = sustainable trend
                anomalies.append({
                    "type": "momentum_acceleration",
                    "momentum_score": momentum_score,
                    "p_value": 0.01,  # High confidence in momentum
                    "expected_return": min(momentum_score * 5, 15),  # Cap at 15%
                    "model": "Regime-Aware Momentum",
                    "regime": "high_momentum_low_vol"
                })
            elif momentum_score < -1.5 and vol_regime > 1.5:  # Negative momentum, high volatility = reversal opportunity
                anomalies.append({
                    "type": "mean_reversion_opportunity",
                    "momentum_score": momentum_score,
                    "p_value": 0.05,
                    "expected_return": -momentum_score * 8,  # Reversion expectation
                    "model": "Regime-Aware Mean Reversion",
                    "regime": "high_vol_negative_momentum"
                })

        # 2. Structural Break Detection (for major market cap changes)
        if len(data) > 100:
            # Look for structural breaks in price levels (like NVIDIA's AI breakthrough)
            price_levels = data['Close'].rolling(window=50).mean()
            recent_level = price_levels.iloc[-10:].mean()
            historical_level = price_levels.iloc[-100:-50].mean()

            level_change = (recent_level / historical_level - 1) * 100 if historical_level > 0 else 0

            if abs(level_change) > 30:  # 30% structural shift
                # This is a regime change, not a reversion opportunity
                anomaly_type = "structural_breakout" if level_change > 30 else "structural_breakdown"

                # For structural breakouts (like NVIDIA), expect continuation not reversion
                expected_return = min(level_change * 0.1, 10) if level_change > 0 else max(level_change * 0.2, -15)

                anomalies.append({
                    "type": anomaly_type,
                    "level_change": level_change,
                    "p_value": 0.02,
                    "expected_return": expected_return,
                    "model": "Structural Break Analysis",
                    "regime": "structural_shift"
                })

        # 3. Only use RSI for genuinely oversold conditions (not overbought in momentum stocks)
        rsi = data['RSI'].dropna()
        if len(rsi) > 0:
            current_rsi = rsi.iloc[-1]

            # Only flag oversold, not overbought (momentum stocks can stay "overbought")
            if current_rsi < 25:  # More extreme threshold
                # Check if this is a momentum stock first
                recent_returns = returns.tail(20).mean() * 252  # Annualized
                if recent_returns < 0.1:  # Only for non-momentum stocks
                    anomalies.append({
                        "type": "technical_oversold",
                        "rsi": current_rsi,
                        "p_value": 0.05,
                        "expected_return": (30 - current_rsi) * 0.8,
                        "model": "RSI Oversold (Non-Momentum)"
                    })

        # 4. Volume-Price Divergence (Jim Simons loved this)
        if 'Volume' in data.columns and len(data) > 30:
            volume_ma = data['Volume'].rolling(window=20).mean()
            price_change = data['Close'].pct_change(20)
            volume_change = (data['Volume'] / volume_ma - 1)

            current_vol_change = volume_change.iloc[-1]
            current_price_change = price_change.iloc[-1]

            # High volume + positive price change = momentum confirmation
            if current_vol_change > 0.5 and current_price_change > 0.1:
                anomalies.append({
                    "type": "volume_momentum_confirmation",
                    "volume_change": current_vol_change,
                    "price_change": current_price_change,
                    "p_value": 0.03,
                    "expected_return": current_price_change * 50,  # Momentum continuation
                    "model": "Volume-Price Momentum"
                })

        return anomalies

class InvestingAnomaliesServer:
    """Main server class for detecting investment anomalies - Jim Simons approach"""
    
    def __init__(self):
        self.server = Server("investing-anomalies-server")
        self.quant_engine = QuantitativeAnalysisEngine()
        self.setup_tools()
        
    def setup_tools(self):
        """Setup MCP tools for the server"""
        
        @self.server.list_tools()
        async def list_tools() -> List[Tool]:
            return [
                Tool(
                    name="detect_market_anomalies",
                    description="Detect statistical market anomalies using Jim Simons' quantitative approach",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "start_date": {"type": "string", "description": "Start date (YYYY-MM-DD)"},
                            "end_date": {"type": "string", "description": "End date (YYYY-MM-DD)"},
                            "exchanges": {"type": "array", "items": {"type": "string"}, "description": "Target exchanges"},
                            "industries": {"type": "array", "items": {"type": "string"}, "description": "Industry filters"},
                            "market_cap_min": {"type": "number", "description": "Minimum market cap in millions EUR"},
                            "volume_threshold": {"type": "number", "description": "Minimum daily volume threshold"}
                        },
                        "required": ["start_date", "end_date", "exchanges"]
                    }
                ),
                Tool(
                    name="analyze_seasonal_patterns",
                    description="Analyze seasonal trading patterns using statistical methods",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "ticker_symbols": {"type": "array", "items": {"type": "string"}},
                            "pattern_type": {"type": "string", "enum": ["monthly", "quarterly", "earnings", "holiday"]},
                            "years_back": {"type": "integer", "description": "Years of historical data"}
                        },
                        "required": ["ticker_symbols", "pattern_type"]
                    }
                ),
                Tool(
                    name="find_value_opportunities",
                    description="Find undervalued companies using quantitative fundamental analysis",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "exchanges": {"type": "array", "items": {"type": "string"}},
                            "pe_ratio_max": {"type": "number"},
                            "debt_equity_max": {"type": "number"},
                            "roe_min": {"type": "number"}
                        },
                        "required": ["exchanges"]
                    }
                )
            ]
        
        @self.server.call_tool()
        async def call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
            try:
                if name == "detect_market_anomalies":
                    result = await self.detect_market_anomalies(**arguments)
                elif name == "analyze_seasonal_patterns":
                    result = await self.analyze_seasonal_patterns(**arguments)
                elif name == "find_value_opportunities":
                    result = await self.find_value_opportunities(**arguments)
                else:
                    raise ValueError(f"Unknown tool: {name}")
                
                return [TextContent(type="text", text=json.dumps(result, indent=2, default=str))]
            except Exception as e:
                logger.error(f"Error in {name}: {str(e)}")
                return [TextContent(type="text", text=f"Error: {str(e)}")]
    
    def _get_sample_stocks(self, exchanges: List[str], industries: List[str] = None) -> List[Dict[str, str]]:
        """Get sample European stocks for analysis"""
        
        amsterdam_stocks = [
            {"ticker": "ASML.AS", "name": "ASML Holding", "exchange": "amsterdam", "industry": "technology"},
            {"ticker": "RDSA.AS", "name": "Royal Dutch Shell", "exchange": "amsterdam", "industry": "energy"},
            {"ticker": "UNILEVER.AS", "name": "Unilever", "exchange": "amsterdam", "industry": "consumer_goods"},
            {"ticker": "ING.AS", "name": "ING Group", "exchange": "amsterdam", "industry": "financial"},
            {"ticker": "ADYEN.AS", "name": "Adyen", "exchange": "amsterdam", "industry": "technology"},
        ]
        
        frankfurt_stocks = [
            {"ticker": "SAP.DE", "name": "SAP SE", "exchange": "frankfurt", "industry": "technology"},
            {"ticker": "SIE.DE", "name": "Siemens AG", "exchange": "frankfurt", "industry": "technology"},
            {"ticker": "ALV.DE", "name": "Allianz SE", "exchange": "frankfurt", "industry": "financial"},
            {"ticker": "BAS.DE", "name": "BASF SE", "exchange": "frankfurt", "industry": "chemical"},
            {"ticker": "VOW3.DE", "name": "Volkswagen AG", "exchange": "frankfurt", "industry": "automotive"},
        ]
        
        stocks = []
        if "amsterdam" in exchanges:
            stocks.extend(amsterdam_stocks)
        if "frankfurt" in exchanges:
            stocks.extend(frankfurt_stocks)
        
        # Filter by industries if specified
        if industries:
            stocks = [s for s in stocks if s["industry"] in industries]
        
        return stocks

    async def detect_market_anomalies(self, start_date: str, end_date: str, 
                                    exchanges: List[str], industries: List[str] = None,
                                    market_cap_min: float = 100, volume_threshold: float = 100000) -> Dict[str, Any]:
        """Main anomaly detection function using Jim Simons' quantitative approach"""
        
        logger.info(f"Detecting anomalies from {start_date} to {end_date} on exchanges: {exchanges}")
        
        # Get real European stocks
        sample_stocks = self._get_sample_stocks(exchanges, industries)
        anomalies = []
        
        for stock in sample_stocks:
            try:
                # Use real quantitative analysis
                anomaly = await self._analyze_stock_with_real_data(stock, start_date, end_date)
                if anomaly and anomaly.statistical_significance < 0.05:  # Only statistically significant
                    anomalies.append(asdict(anomaly))
            except Exception as e:
                logger.warning(f"Error analyzing {stock['ticker']}: {str(e)}")
        
        # Sort by statistical significance (lower p-value = more significant)
        anomalies.sort(key=lambda x: x['statistical_significance'])
        
        return {
            "analysis_period": f"{start_date} to {end_date}",
            "exchanges_analyzed": exchanges,
            "total_anomalies_found": len(anomalies),
            "statistically_significant": len([a for a in anomalies if a['statistical_significance'] < 0.01]),
            "anomalies": anomalies[:20],  # Top 20 anomalies
            "summary": self._generate_quant_summary(anomalies)
        }

    async def _analyze_stock_with_real_data(self, stock: Dict[str, str], start_date: str, end_date: str) -> Optional[AnomalyResult]:
        """Analyze stock using real market data and quantitative methods"""

        ticker = stock["ticker"]
        logger.info(f"Analyzing {ticker} with real market data")

        # Get real market data
        data = self.quant_engine.get_real_market_data(ticker, period="2y")
        if data.empty:
            return None

        # Detect statistical anomalies
        anomalies = self.quant_engine.detect_statistical_anomalies(data, ticker)
        if not anomalies:
            return None

        # Take the most significant anomaly
        best_anomaly = min(anomalies, key=lambda x: x.get('p_value', 1.0))

        # Calculate additional metrics
        returns = data['Returns'].dropna()
        current_price = data['Close'].iloc[-1]

        # Calculate Sharpe ratio (assuming risk-free rate of 2%)
        excess_returns = returns - 0.02/252  # Daily risk-free rate
        sharpe_ratio = excess_returns.mean() / excess_returns.std() * np.sqrt(252) if excess_returns.std() > 0 else 0

        # Calculate maximum drawdown
        cumulative = (1 + returns).cumprod()
        running_max = cumulative.expanding().max()
        drawdown = (cumulative - running_max) / running_max
        max_drawdown = drawdown.min()

        # Calculate volatility
        volatility = returns.std() * np.sqrt(252)

        # Predict price based on expected return
        expected_return = best_anomaly.get('expected_return', 0) / 100
        predicted_price = current_price * (1 + expected_return)

        # Calculate confidence interval (95%)
        price_std = current_price * volatility / np.sqrt(252) * 30  # 30-day horizon
        ci_lower = predicted_price - 1.96 * price_std
        ci_upper = predicted_price + 1.96 * price_std

        return AnomalyResult(
            company_name=stock["name"],
            ticker_symbol=ticker,
            exchange=stock["exchange"],
            industry=stock["industry"],
            anomaly_type=best_anomaly["type"],
            statistical_significance=best_anomaly["p_value"],
            z_score=best_anomaly.get("z_score", 0),
            sharpe_ratio=sharpe_ratio,
            information_ratio=sharpe_ratio,  # Simplified
            current_price=current_price,
            predicted_price=predicted_price,
            confidence_interval=(ci_lower, ci_upper),
            expected_return=expected_return * 100,
            max_drawdown=max_drawdown * 100,
            volatility=volatility * 100,
            description=self._generate_quant_description(best_anomaly, stock["name"]),
            mathematical_model=best_anomaly["model"],
            supporting_data={
                "rsi": data['RSI'].iloc[-1] if 'RSI' in data.columns else None,
                "volatility_percentile": stats.percentileofscore(data['Volatility'].dropna(), data['Volatility'].iloc[-1]),
                "price_vs_sma20": (current_price / data['SMA_20'].iloc[-1] - 1) * 100,
                "volume_avg": data['Volume'].tail(20).mean(),
                "data_points": len(data),
                "analysis_date": datetime.now().isoformat()
            }
        )

    def _generate_quant_description(self, anomaly: Dict[str, Any], company_name: str) -> str:
        """Generate quantitative description of the anomaly"""
        descriptions = {
            "mean_reversion_oversold": f"{company_name} shows statistically significant oversold condition (Z-score: {anomaly.get('z_score', 0):.2f}). Mean reversion model suggests upward price correction.",
            "mean_reversion_overbought": f"{company_name} exhibits overbought condition with Z-score {anomaly.get('z_score', 0):.2f}. Statistical model indicates potential downward correction.",
            "technical_oversold": f"{company_name} RSI at {anomaly.get('rsi', 0):.1f} indicates oversold condition. Technical analysis suggests reversal probability.",
            "technical_overbought": f"{company_name} RSI at {anomaly.get('rsi', 0):.1f} shows overbought condition. Technical indicators suggest downward pressure.",
            "volatility_anomaly": f"{company_name} experiencing abnormal volatility (Z-score: {anomaly.get('z_score', 0):.2f}). Volatility model indicates regime change."
        }
        return descriptions.get(anomaly["type"], f"Statistical anomaly detected in {company_name}")

    def _generate_quant_summary(self, anomalies: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate quantitative summary of anomalies"""
        if not anomalies:
            return {"message": "No statistically significant anomalies detected"}

        # Calculate summary statistics
        p_values = [a['statistical_significance'] for a in anomalies]
        expected_returns = [a['expected_return'] for a in anomalies]
        sharpe_ratios = [a['sharpe_ratio'] for a in anomalies]

        # Count anomaly types
        anomaly_types = {}
        for anomaly in anomalies:
            atype = anomaly['anomaly_type']
            anomaly_types[atype] = anomaly_types.get(atype, 0) + 1

        top_anomaly = anomalies[0] if anomalies else None

        return {
            "total_significant_anomalies": len(anomalies),
            "avg_p_value": np.mean(p_values),
            "avg_expected_return": np.mean(expected_returns),
            "avg_sharpe_ratio": np.mean(sharpe_ratios),
            "anomaly_types_distribution": anomaly_types,
            "most_significant_anomaly": {
                "ticker": top_anomaly['ticker_symbol'],
                "company": top_anomaly['company_name'],
                "p_value": top_anomaly['statistical_significance'],
                "expected_return": top_anomaly['expected_return']
            } if top_anomaly else None,
            "recommendation": self._generate_quant_recommendation(anomalies)
        }

    def _generate_quant_recommendation(self, anomalies: List[Dict[str, Any]]) -> str:
        """Generate quantitative investment recommendation"""
        if not anomalies:
            return "No actionable anomalies detected. Continue monitoring."

        high_significance = [a for a in anomalies if a['statistical_significance'] < 0.01]
        positive_expected = [a for a in anomalies if a['expected_return'] > 0]
        high_sharpe = [a for a in anomalies if a['sharpe_ratio'] > 1.0]

        if high_significance and positive_expected:
            return f"Found {len(high_significance)} highly significant anomalies with positive expected returns. Focus on mean reversion and technical oversold opportunities."
        elif positive_expected:
            return f"Identified {len(positive_expected)} opportunities with positive expected returns. Apply position sizing based on statistical significance."
        else:
            return "Detected anomalies primarily indicate overvalued conditions. Consider short positions or wait for better entry points."

    async def analyze_seasonal_patterns(self, ticker_symbols: List[str], pattern_type: str, years_back: int = 5) -> Dict[str, Any]:
        """Analyze seasonal patterns using real data"""
        logger.info(f"Analyzing {pattern_type} patterns for {len(ticker_symbols)} stocks")

        patterns = {}
        for ticker in ticker_symbols:
            try:
                data = self.quant_engine.get_real_market_data(ticker, period=f"{years_back}y")
                if not data.empty:
                    pattern = self._calculate_seasonal_pattern(data, pattern_type)
                    patterns[ticker] = pattern
            except Exception as e:
                logger.warning(f"Error analyzing seasonal patterns for {ticker}: {str(e)}")

        return {
            "pattern_type": pattern_type,
            "analysis_period_years": years_back,
            "stocks_analyzed": len(patterns),
            "patterns": patterns,
            "summary": {
                "stocks_with_patterns": len(patterns),
                "recommendation": "Consider seasonal timing for entry/exit points based on historical patterns"
            }
        }

    def _calculate_seasonal_pattern(self, data: pd.DataFrame, pattern_type: str) -> Dict[str, Any]:
        """Calculate seasonal patterns from real data"""
        if pattern_type == "monthly":
            data['Month'] = data.index.month
            monthly_returns = data.groupby('Month')['Returns'].mean() * 100

            return {
                "monthly_avg_returns": {f"month_{i}": monthly_returns.get(i, 0) for i in range(1, 13)},
                "best_performing_month": f"month_{monthly_returns.idxmax()}",
                "worst_performing_month": f"month_{monthly_returns.idxmin()}",
                "seasonal_strength": monthly_returns.std() / abs(monthly_returns.mean()) if monthly_returns.mean() != 0 else 0,
                "statistical_significance": 0.05  # Simplified
            }

        return {"pattern": "Not implemented for this pattern type"}

    async def find_value_opportunities(self, exchanges: List[str], pe_ratio_max: float = 15,
                                     debt_equity_max: float = 0.5, roe_min: float = 0.15) -> Dict[str, Any]:
        """Find value opportunities using real fundamental data"""
        logger.info(f"Searching for value opportunities on {exchanges}")

        # For now, return a simplified response since we'd need fundamental data APIs
        return {
            "screening_criteria": {
                "max_pe_ratio": pe_ratio_max,
                "max_debt_equity": debt_equity_max,
                "min_roe": roe_min
            },
            "opportunities_found": 0,
            "value_opportunities": [],
            "summary": {
                "message": "Real fundamental data screening requires additional data sources. Consider integrating with financial data providers."
            }
        }

async def main():
    """Main entry point for the MCP server"""
    server = InvestingAnomaliesServer()
    
    # Run the server
    from mcp.server.stdio import stdio_server
    async with stdio_server() as (read_stream, write_stream):
        await server.server.run(read_stream, write_stream, server.server.create_initialization_options())

if __name__ == "__main__":
    asyncio.run(main())
