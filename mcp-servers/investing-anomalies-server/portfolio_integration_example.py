#!/usr/bin/env python3
"""
Portfolio Integration Example
Demonstrates how to use the Investing Anomalies MCP Server with your DEGIRO portfolio
"""

import asyncio
import json
from datetime import datetime, timedelta
from server import InvestingAnomaliesServer

class PortfolioAnalyzer:
    """Integrates anomaly detection with your actual DEGIRO portfolio"""
    
    def __init__(self):
        self.server = InvestingAnomaliesServer()
        
        # Your current DEGIRO portfolio holdings (from your analysis)
        self.portfolio = {
            "european_holdings": [
                {"ticker": "ASML.AS", "name": "ASML Holding", "shares": 0, "exchange": "amsterdam"},
                {"ticker": "RDSA.AS", "name": "Royal Dutch Shell", "shares": 0, "exchange": "amsterdam"},
                {"ticker": "ING.AS", "name": "ING Group", "shares": 0, "exchange": "amsterdam"},
                {"ticker": "ADYEN.AS", "name": "Adyen", "shares": 0, "exchange": "amsterdam"},
                # Add any German holdings you might have
                {"ticker": "SAP.DE", "name": "SAP SE", "shares": 0, "exchange": "frankfurt"},
            ],
            "us_holdings": [
                {"ticker": "NVDA", "name": "NVIDIA", "shares": 4},
                {"ticker": "PLTR", "name": "Palantir", "shares": 2},
                {"ticker": "TTD", "name": "Trade Desk", "shares": 5},
                {"ticker": "VERI", "name": "Veritone", "shares": 100},
                {"ticker": "BRYN", "name": "Berkshire Hathaway", "shares": 2},
            ]
        }
    
    async def analyze_european_holdings(self):
        """Analyze your European holdings for anomalies"""
        print("🔍 Analyzing European Holdings for Anomalies...")
        
        if not self.portfolio["european_holdings"]:
            print("   No European holdings found in portfolio")
            return
        
        # Get tickers for analysis
        european_tickers = [holding["ticker"] for holding in self.portfolio["european_holdings"]]
        
        # Analyze for anomalies
        start_date = (datetime.now() - timedelta(days=90)).strftime("%Y-%m-%d")
        end_date = datetime.now().strftime("%Y-%m-%d")
        
        result = await self.server.detect_market_anomalies(
            start_date=start_date,
            end_date=end_date,
            exchanges=["amsterdam", "frankfurt"],
            industries=["technology", "financial", "energy"],
            market_cap_min=1000
        )
        
        print(f"   📊 Found {result['total_anomalies_found']} anomalies in European markets")
        
        # Filter for your holdings
        your_anomalies = [
            anomaly for anomaly in result['anomalies'] 
            if anomaly['ticker_symbol'] in european_tickers
        ]
        
        if your_anomalies:
            print(f"   🎯 {len(your_anomalies)} anomalies found in YOUR holdings:")
            for anomaly in your_anomalies:
                print(f"      • {anomaly['company_name']}: {anomaly['anomaly_type']} (Confidence: {anomaly['confidence_score']}/10)")
        else:
            print("   ✅ No significant anomalies detected in your European holdings")
        
        return your_anomalies
    
    async def find_european_opportunities(self):
        """Find new European investment opportunities"""
        print("\n💎 Screening for European Value Opportunities...")
        
        result = await self.server.find_value_opportunities(
            exchanges=["amsterdam", "frankfurt"],
            pe_ratio_max=15,
            debt_equity_max=0.5,
            roe_min=0.15
        )
        
        print(f"   📊 Found {result['opportunities_found']} value opportunities")
        
        if result['value_opportunities']:
            print("   🎯 Top 3 opportunities:")
            for i, opp in enumerate(result['value_opportunities'][:3], 1):
                print(f"      {i}. {opp['company_name']} ({opp['ticker']})")
                print(f"         Value Score: {opp['value_score']}/10, P/E: {opp['pe_ratio']}, ROE: {opp['roe']:.1%}")
        
        return result['value_opportunities']
    
    async def analyze_seasonal_patterns(self):
        """Analyze seasonal patterns for your holdings"""
        print("\n🗓️ Analyzing Seasonal Patterns...")
        
        # Combine European and US tickers (for demonstration)
        all_tickers = [holding["ticker"] for holding in self.portfolio["european_holdings"]]
        
        if all_tickers:
            result = await self.server.analyze_seasonal_patterns(
                ticker_symbols=all_tickers[:3],  # Limit to 3 for demo
                pattern_type="monthly",
                years_back=5
            )
            
            print(f"   📊 Analyzed {result['stocks_analyzed']} stocks for monthly patterns")
            
            # Show best performing months
            for ticker, pattern in result['patterns'].items():
                if 'best_performing_month' in pattern:
                    print(f"      • {ticker}: Best month is {pattern['best_performing_month']}")
        
        return result if all_tickers else None
    
    async def generate_trading_recommendations(self):
        """Generate actionable trading recommendations"""
        print("\n🎯 Generating Trading Recommendations...")
        
        recommendations = []
        
        # Analyze anomalies
        anomalies = await self.analyze_european_holdings()
        if anomalies:
            for anomaly in anomalies:
                if anomaly['confidence_score'] >= 7.5 and anomaly['risk_level'] in ['Low', 'Medium']:
                    recommendations.append({
                        "action": "MONITOR",
                        "ticker": anomaly['ticker_symbol'],
                        "reason": f"{anomaly['anomaly_type']} detected",
                        "confidence": anomaly['confidence_score'],
                        "target_price": anomaly['target_price']
                    })
        
        # Find new opportunities
        opportunities = await self.find_european_opportunities()
        if opportunities:
            for opp in opportunities[:2]:  # Top 2 opportunities
                if opp['value_score'] >= 8.0:
                    recommendations.append({
                        "action": "CONSIDER_BUY",
                        "ticker": opp['ticker'],
                        "reason": f"Value opportunity (Score: {opp['value_score']}/10)",
                        "confidence": opp['value_score'],
                        "target_price": opp['fair_value_estimate']
                    })
        
        # Display recommendations
        if recommendations:
            print("   📋 Actionable Recommendations:")
            for i, rec in enumerate(recommendations, 1):
                print(f"      {i}. {rec['action']}: {rec['ticker']}")
                print(f"         Reason: {rec['reason']}")
                print(f"         Confidence: {rec['confidence']}/10")
                if rec.get('target_price'):
                    print(f"         Target Price: €{rec['target_price']}")
        else:
            print("   ✅ No immediate action required - portfolio looks stable")
        
        return recommendations
    
    async def run_complete_analysis(self):
        """Run complete portfolio analysis"""
        print("🚀 DEGIRO Portfolio Analysis with Anomaly Detection")
        print("=" * 60)
        
        # Run all analyses
        await self.analyze_european_holdings()
        await self.find_european_opportunities()
        await self.analyze_seasonal_patterns()
        recommendations = await self.generate_trading_recommendations()
        
        print("\n" + "=" * 60)
        print("📊 ANALYSIS COMPLETE")
        print("=" * 60)
        
        print(f"\n💡 Summary:")
        print(f"   • European holdings analyzed: {len(self.portfolio['european_holdings'])}")
        print(f"   • US holdings (for reference): {len(self.portfolio['us_holdings'])}")
        print(f"   • Actionable recommendations: {len(recommendations)}")
        
        print(f"\n🎯 Next Steps:")
        print(f"   1. Review recommendations above")
        print(f"   2. Set up alerts for high-confidence anomalies")
        print(f"   3. Consider diversifying into European value opportunities")
        print(f"   4. Monitor seasonal patterns for optimal entry/exit timing")
        
        return {
            "european_analysis": True,
            "recommendations": recommendations,
            "total_opportunities": len(recommendations)
        }

async def main():
    """Main execution function"""
    analyzer = PortfolioAnalyzer()
    await analyzer.run_complete_analysis()

if __name__ == "__main__":
    asyncio.run(main())
