#!/usr/bin/env python3
"""
Setup script for the Investing Anomalies MCP Server
"""

from setuptools import setup, find_packages
import os

# Read the README file
def read_readme():
    with open("README.md", "r", encoding="utf-8") as fh:
        return fh.read()

# Read requirements
def read_requirements():
    with open("requirements.txt", "r", encoding="utf-8") as fh:
        return [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="investing-anomalies-server",
    version="1.0.0",
    author="Investment Analysis Team",
    author_email="<EMAIL>",
    description="Quantitative investment analysis MCP server for detecting market anomalies",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/investment-team/investing-anomalies-server",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Financial and Insurance Industry",
        "Topic :: Office/Business :: Financial :: Investment",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
    ],
    python_requires=">=3.8",
    install_requires=read_requirements(),
    entry_points={
        "console_scripts": [
            "investing-anomalies-server=server:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["*.json", "*.md", "*.txt"],
    },
    keywords="investing, anomalies, quantitative, finance, mcp, server, european, stocks",
    project_urls={
        "Bug Reports": "https://github.com/investment-team/investing-anomalies-server/issues",
        "Source": "https://github.com/investment-team/investing-anomalies-server",
        "Documentation": "https://github.com/investment-team/investing-anomalies-server/wiki",
    },
)
