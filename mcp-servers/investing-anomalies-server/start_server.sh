#!/bin/bash
# MCP Server Startup Script for Investing Anomalies Server

# Set the correct Python path
export PATH="/Users/<USER>/.pyenv/versions/3.11.3/bin:$PATH"
export PYTHONPATH="/private/var/www/2025/ollamar1/beauty-crm/mcp-servers/investing-anomalies-server"

# Change to server directory
cd "/private/var/www/2025/ollamar1/beauty-crm/mcp-servers/investing-anomalies-server"

# Start the MCP server
exec python3 server.py "$@"
