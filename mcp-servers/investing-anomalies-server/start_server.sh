#!/bin/bash
# Wrapper script for investing-anomalies MCP server
# This ensures the correct environment is loaded

# Add pyenv Python to PATH
export PATH="/Users/<USER>/.pyenv/versions/3.11.3/bin:$PATH"

# Set Python path for our server
export PYTHONPATH="/private/var/www/2025/ollamar1/beauty-crm/mcp-servers/investing-anomalies-server"

# Run the investing-anomalies server with full path
exec "/Users/<USER>/.pyenv/versions/3.11.3/bin/python3" "/private/var/www/2025/ollamar1/beauty-crm/mcp-servers/investing-anomalies-server/server.py" "$@"
