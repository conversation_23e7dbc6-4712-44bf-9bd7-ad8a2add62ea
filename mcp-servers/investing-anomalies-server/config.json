{"server_info": {"name": "investing-anomalies-server", "version": "1.0.0", "description": "Quantitative investment analysis server for detecting market anomalies", "author": "Investment Analysis Team", "license": "MIT"}, "supported_exchanges": {"amsterdam": {"name": "Euronext Amsterdam", "currency": "EUR", "timezone": "Europe/Amsterdam", "trading_hours": {"open": "09:00", "close": "17:30"}, "sample_stocks": [{"ticker": "ASML.AS", "name": "ASML Holding", "industry": "technology"}, {"ticker": "RDSA.AS", "name": "Royal Dutch Shell", "industry": "energy"}, {"ticker": "UNILEVER.AS", "name": "Unilever", "industry": "consumer_goods"}, {"ticker": "ING.AS", "name": "ING Group", "industry": "financial"}, {"ticker": "ADYEN.AS", "name": "<PERSON><PERSON><PERSON>", "industry": "technology"}]}, "frankfurt": {"name": "Frankfurt Stock Exchange (XETRA)", "currency": "EUR", "timezone": "Europe/Berlin", "trading_hours": {"open": "09:00", "close": "17:30"}, "sample_stocks": [{"ticker": "SAP.DE", "name": "SAP SE", "industry": "technology"}, {"ticker": "SIE.DE", "name": "Siemens AG", "industry": "industrial"}, {"ticker": "ALV.DE", "name": "Allianz SE", "industry": "financial"}, {"ticker": "BAS.DE", "name": "BASF SE", "industry": "chemicals"}, {"ticker": "VOW3.DE", "name": "Volkswagen AG", "industry": "automotive"}]}}, "anomaly_types": {"seasonal_pattern": {"description": "Recurring calendar-based patterns", "typical_duration_days": 30, "confidence_threshold": 7.0, "risk_level": "Medium"}, "technical_oversold": {"description": "RSI below 30 with reversal signals", "typical_duration_days": 45, "confidence_threshold": 7.5, "risk_level": "Low"}, "fundamental_disconnect": {"description": "Price below intrinsic value", "typical_duration_days": 90, "confidence_threshold": 8.0, "risk_level": "Low"}, "volume_spike": {"description": "Unusual volume with price momentum", "typical_duration_days": 15, "confidence_threshold": 6.5, "risk_level": "High"}, "earnings_surprise": {"description": "Likely earnings beats based on sector trends", "typical_duration_days": 30, "confidence_threshold": 7.0, "risk_level": "Medium"}, "sector_rotation": {"description": "Beneficiaries of sector rotation", "typical_duration_days": 60, "confidence_threshold": 7.5, "risk_level": "Medium"}}, "screening_criteria": {"value_investing": {"pe_ratio_max": 15, "debt_equity_max": 0.5, "roe_min": 0.15, "current_ratio_min": 1.2, "profit_margin_min": 0.1}, "growth_investing": {"revenue_growth_min": 0.15, "earnings_growth_min": 0.2, "pe_ratio_max": 30, "peg_ratio_max": 1.5}, "dividend_investing": {"dividend_yield_min": 0.03, "payout_ratio_max": 0.7, "dividend_growth_years": 5, "debt_equity_max": 0.6}}, "risk_levels": {"Low": {"confidence_score_min": 8.5, "historical_success_rate_min": 75, "max_position_size": 0.05}, "Medium": {"confidence_score_min": 7.0, "historical_success_rate_min": 65, "max_position_size": 0.03}, "High": {"confidence_score_min": 6.0, "historical_success_rate_min": 55, "max_position_size": 0.02}}, "integration_settings": {"degiro_compatible": true, "cache_duration_minutes": 30, "max_concurrent_requests": 10, "rate_limit_per_minute": 60, "enable_backtesting": true, "enable_paper_trading": true}, "data_sources": {"primary": "GroupA-investor-agent", "news": "perplexity-ask-web-search", "fundamental": "get_ticker_data", "historical": "get_price_history", "options": "get_options"}}