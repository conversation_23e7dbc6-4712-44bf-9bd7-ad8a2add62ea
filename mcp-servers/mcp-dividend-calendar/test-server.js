#!/usr/bin/env node

/**
 * Test script for DivvyDiary + DEGIRO MCP Server
 */

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

async function testMCPServer() {
  console.log('🧪 Testing DivvyDiary + DEGIRO MCP Server...\n');

  const serverPath = join(__dirname, 'divvy-degiro-mcp-server.js');
  
  // Test 1: List available tools
  console.log('📋 Test 1: Listing available tools...');
  const listToolsRequest = {
    jsonrpc: '2.0',
    id: 1,
    method: 'tools/list'
  };

  await sendMCPRequest(serverPath, listToolsRequest);

  // Test 2: Scan dividend calendar for next 7 days
  console.log('\n🔍 Test 2: Scanning dividend calendar...');
  const today = new Date().toISOString().split('T')[0];
  const nextWeek = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
  
  const scanRequest = {
    jsonrpc: '2.0',
    id: 2,
    method: 'tools/call',
    params: {
      name: 'scan_dividend_calendar',
      arguments: {
        startDate: today,
        endDate: nextWeek,
        minYield: 5,
        minDividend: 0.5,
        maxResults: 10
      }
    }
  };

  await sendMCPRequest(serverPath, scanRequest);

  // Test 3: Find dividend beasts
  console.log('\n🦁 Test 3: Finding dividend beasts...');
  const beastsRequest = {
    jsonrpc: '2.0',
    id: 3,
    method: 'tools/call',
    params: {
      name: 'find_dividend_beasts',
      arguments: {
        dateRange: 'month',
        sortBy: 'yield',
        filterEuropean: true
      }
    }
  };

  await sendMCPRequest(serverPath, beastsRequest);

  console.log('\n🎉 All tests completed!');
}

function sendMCPRequest(serverPath, request) {
  return new Promise((resolve, reject) => {
    const server = spawn('node', [serverPath], {
      stdio: ['pipe', 'pipe', 'pipe']
    });

    let output = '';
    let errorOutput = '';

    server.stdout.on('data', (data) => {
      output += data.toString();
    });

    server.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });

    server.on('close', (code) => {
      if (code === 0) {
        try {
          const response = JSON.parse(output);
          console.log('✅ Response:', JSON.stringify(response, null, 2));
          resolve(response);
        } catch (error) {
          console.log('📄 Raw output:', output);
          resolve(output);
        }
      } else {
        console.error('❌ Error:', errorOutput);
        reject(new Error(`Server exited with code ${code}`));
      }
    });

    // Send the request
    server.stdin.write(JSON.stringify(request) + '\n');
    server.stdin.end();

    // Timeout after 30 seconds
    setTimeout(() => {
      server.kill();
      reject(new Error('Request timeout'));
    }, 30000);
  });
}

// Run tests
testMCPServer().catch(console.error);
